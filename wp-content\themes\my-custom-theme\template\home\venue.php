<?php
/**
 * Template part for displaying venue section on home page
 */
?>

<!-- venue area -->
<div class="venue-area py-120">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="venue-content wow fadeInLeft" data-wow-delay=".25s">
                    <div class="site-heading mb-3">
                        <span class="site-title-tagline">Event Venue</span>
                        <h2 class="site-title">
                            Our Event <span>Venue</span> Location
                        </h2>
                        <div class="site-shadow-text wow fadeInRight" data-wow-delay=".35s">Venue</div>
                    </div>
                    <p>There are many variations of passages the majority have suffered alteration
                         in some form slightly believable. If you are going to use a passage of need to be sure. 
                         All the generators on the Internet tend to repeat predefined chunks.</p>
                    <div class="venue-list">
                        <ul>
                            <li><i class="far fa-check-circle"></i> 25/B Milford Road, New York</li>
                            <li><i class="far fa-check-circle"></i> +2 123 654 7898</li>
                            <li><i class="far fa-check-circle"></i> <EMAIL></li>
                            <li><i class="far fa-check-circle"></i> www.example.com</li>
                        </ul>
                    </div>
                    <a href="contact.html" class="theme-btn mt-30">Get Direction<i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="venue-img wow fadeInRight" data-wow-delay=".25s">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/img/venue/01.jpg" alt="">
                </div>
            </div>
        </div>
    </div>
</div>
<!-- venue area end -->
