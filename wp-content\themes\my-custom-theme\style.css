/*
Theme Name: Custom Theme
Author: You
Description: A custom WordPress theme with dynamic header and footer.
Version: 1.0
*/

/* Custom WordPress Theme Styles - Only add custom overrides here */

/* Ensure proper page structure and scrolling */
html, body {
    overflow-x: hidden; /* Prevent horizontal scroll */
    scroll-behavior: smooth;
}

body {
    position: relative;
}

/* Ensure main content has proper spacing */
.site-main {
    min-height: 50vh; /* Ensure there's content to scroll to */
    position: relative;
    z-index: 1;
}

/* Footer styling */
.footer-area {
    background: var(--footer-bg, #01103B);
    color: var(--footer-text-color, #F5FAFF);
    margin-top: 50px;
}

/* Custom widget styling to match template */
.footer-widget-box.about-us .footer-logo img {
    max-width: 150px;
    height: auto;
}

.footer-widget-box.about-us .footer-logo h3 {
    color: white;
    margin-bottom: 20px;
}

.footer-newsletter .subscribe-form .form-group {
    position: relative;
    display: flex;
    gap: 10px;
}

.footer-newsletter .subscribe-form .form-control {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 25px;
}

.footer-newsletter .subscribe-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.footer-newsletter .subscribe-form .theme-btn {
    white-space: nowrap;
    padding: 12px 20px;
}

/* Ensure footer lists display properly */
.footer-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-list li {
    margin-bottom: 8px;
}

.footer-list li a {
    color: var(--footer-text-color, #F5FAFF);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-list li a:hover {
    color: var(--theme-color, #FC226A);
}

.footer-contact li {
    margin-bottom: 12px;
    color: var(--footer-text-color, #F5FAFF);
}

.footer-contact li i {
    margin-right: 10px;
    color: var(--theme-color, #FC226A);
}


