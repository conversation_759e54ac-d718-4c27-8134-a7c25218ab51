/*
Theme Name: Custom Theme
Author: You
Description: A custom WordPress theme with dynamic header and footer.
Version: 1.0
*/

/* CSS Variables */
:root {
  --body-font: '<PERSON><PERSON>', sans-serif;
  --heading-font: "Poppins", sans-serif;
  --theme-color: #FC226A;
  --theme-color2: #8C52FF;
  --theme-bg-light: #F8F9FE;
  --color-dark: #01054C;
  --color-gray: #F6F6F6;
  --body-text-color: #757F95;
  --color-white: #ffffff;
  --hero-overlay: rgb(5, 3, 17);
  --slider-arrow-bg: rgba(140, 82, 255, .2);
  --box-shadow: 0 0 40px 5px rgb(0 0 0 / 5%);
  --box-shadow2: 0 0 15px rgba(0, 0, 0, 0.17);
  --transition: all .5s ease-in-out;
  --transition2: all .3s ease-in-out;
  --border-info-color: rgba(0, 0, 0, 0.08);
  --border-info-color2: rgba(0, 0, 0, 0.05);
  --border-white-color: rgba(255, 255, 255, 0.08);
  --border-white-color2: rgba(255, 255, 255, 0.05);
  --footer-bg: #01103B;
  --footer-bg2: #00134C;
  --footer-text-color: #F5FAFF;
  --theme-gradient: linear-gradient(to right, #fc226a 0%, #8c52ff 100%);
}
/* Hero Section Styles */
.hero-section {
    position: relative;
}

.hero-scroll-box{
    position: absolute;
    left: 55px;
    bottom: 50px;
    z-index: 2;
}

.hero-scroll{
    width: 30px;
    height: 60px;
    border: 3px solid var(--color-white);
    border-radius: 15px;
    position: relative;
}

.hero-scroll .scroller{
    width: 16px;
    border-radius: 8px;
    background-color: var(--color-white);
    position: absolute;
    top: 4px;
    left: 4px;
    bottom: 34px;
    animation: scroller 1500ms ease-out infinite;
}

@keyframes scroller{
    0%{
        bottom: 34px;
    }
    5%{
        top: 4px;
    }
    32%{
        bottom: 4px;
    }
    66%{
        top: 34px;
        bottom: 4px;
    }
    100%{
        top: 4px;
        bottom: 34px;
    }
}

.hero-single {
    padding-top: 150px;
    padding-bottom: 160px;
    background-position: center !important;
    background-size: cover !important;
    background-repeat: no-repeat !important;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.hero-single::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: -0.5px;
    top: 0;
    background: var(--hero-overlay);
    opacity: 0.7;
    z-index: -1;
}

.hero-single .hero-content {
    height: 100%;
}

.hero-date{
    display: flex;
    align-items: center;
    gap: 15px;
}

.hero-date h1{
    color: var(--theme-color);
    font-size: 90px;
    font-weight: 700;
}

.hero-date .date-content span{
    color: var(--color-white);
    text-transform: uppercase;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 1px;
}

.hero-date .date-content p{
    color: var(--color-white);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-single .hero-content .hero-title {
    color: var(--color-white);
    font-size: 60px;
    font-weight: 700;
    margin: 20px 0;
    text-transform: uppercase;
}

.hero-single .hero-content .hero-title span{
    color: var(--theme-color);
    font-weight: 500;
}

.hero-single .hero-content p {
    color: var(--color-white);
    font-size: 20px;
    line-height: 30px;
    font-weight: 400;
    margin-bottom: 20px;
}

.hero-single .hero-content .hero-btn {
    gap: 1rem;
    display: flex;
    margin-top: 35px;
    justify-content: start;
}

/* Owl Carousel Navigation */
.hero-slider.owl-theme .owl-nav {
    margin-top: 0px;
}

.hero-slider.owl-theme .owl-nav [class*=owl-] {
    color: var(--color-white);
    font-size: 25px;
    margin: 0;
    padding: 0;
    background: var(--slider-arrow-bg);
    display: inline-block;
    cursor: pointer;
    height: 55px;
    width: 55px;
    line-height: 55px;
    border-radius: 50px;
    text-align: center;
    transition: var(--transition);
}

.hero-slider.owl-theme .owl-nav [class*=owl-]:hover {
    background: var(--color-white);
    color: var(--theme-color);
}

.hero-slider.owl-theme .owl-nav .owl-prev {
    left: 40px;
}

.hero-slider.owl-theme .owl-nav .owl-next {
    right: 40px;
}

.hero-slider.owl-theme .owl-nav .owl-prev,
.hero-slider.owl-theme .owl-nav .owl-next {
    position: absolute;
    top: 50%;
    transform: translate(0, -50%);
}

.hero-slider.owl-theme .owl-dots {
    position: absolute;
    text-align: center;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
}

.hero-slider.owl-theme .owl-dots .owl-dot span {
    background: var(--color-white);
    margin: 5px;
    border-radius: 10px;
    width: 15px;
    height: 15px;
    display: inline-block;
    transition: var(--transition);
}

.hero-slider.owl-theme .owl-dots .owl-dot.active span {
    background-color: var(--theme-color);
}

/* Responsive Styles */
@media all and (max-width: 1199px) {
    .hero-single .hero-content .hero-title {
        font-size: 37px;
    }

    .hero-slider.owl-theme .owl-nav .owl-prev,
    .hero-slider.owl-theme .owl-nav .owl-next {
        top: unset;
        bottom: 70px !important;
    }

    .hero-slider.owl-theme .owl-nav .owl-prev {
        left: unset;
        right: 120px;
    }

    .hero-slider.owl-theme .owl-nav .owl-next {
        right: 40px;
    }
}

@media all and (max-width: 991px) {
    .hero-single .hero-content .hero-title {
        font-size: 45px;
    }

    .hero-scroll-box{
        left: 20px;
        bottom: 80px;
    }
}

@media all and (max-width: 767px) {
    .hero-single .hero-content .hero-sub-title {
        font-size: 18px;
    }

    .hero-single .hero-content .hero-btn {
        gap: 1rem;
    }
}


