<?php
/**
 * Custom Widgets for Footer
 */

// Newsletter Widget
class Mytheme_Newsletter_Widget extends WP_Widget {
    
    public function __construct() {
        parent::__construct(
            'mytheme_newsletter',
            __('Footer Newsletter', 'mytheme'),
            array('description' => __('Newsletter subscription widget for footer', 'mytheme'))
        );
    }
    
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        // Logo
        if (has_custom_logo()) {
            $custom_logo_id = get_theme_mod('custom_logo');
            $logo = wp_get_attachment_image_src($custom_logo_id, 'full');
            echo '<a href="' . esc_url(home_url('/')) . '" class="footer-logo">';
            echo '<img src="' . esc_url($logo[0]) . '" alt="' . get_bloginfo('name') . '">';
            echo '</a>';
        } else {
            echo '<a href="' . esc_url(home_url('/')) . '" class="footer-logo">';
            echo '<h3 style="color: white;">' . get_bloginfo('name') . '</h3>';
            echo '</a>';
        }
        
        // Description
        $description = !empty($instance['description']) ? $instance['description'] : 'We are many variations of passages available majority have suffered in some injected content of a page when looking at its layout humour words believable.';
        echo '<p class="mb-3">' . esc_html($description) . '</p>';
        
        // Newsletter
        echo '<div class="footer-newsletter">';
        echo '<p>Subscribe Our Newsletter</p>';
        echo '<div class="subscribe-form">';
        echo '<form action="#" method="post">';
        echo '<div class="form-group">';
        echo '<input type="email" class="form-control" placeholder="Your Email" required>';
        echo '<button class="theme-btn" type="submit">';
        echo '<span class="far fa-paper-plane"></span> Subscribe';
        echo '</button>';
        echo '</div>';
        echo '</form>';
        echo '</div>';
        echo '</div>';
        
        echo $args['after_widget'];
    }
    
    public function form($instance) {
        $description = !empty($instance['description']) ? $instance['description'] : '';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('description'); ?>"><?php _e('Description:', 'mytheme'); ?></label>
            <textarea class="widefat" id="<?php echo $this->get_field_id('description'); ?>" name="<?php echo $this->get_field_name('description'); ?>" rows="4"><?php echo esc_attr($description); ?></textarea>
        </p>
        <?php
    }
    
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['description'] = (!empty($new_instance['description'])) ? sanitize_textarea_field($new_instance['description']) : '';
        return $instance;
    }
}

// Quick Links Widget
class Mytheme_Quick_Links_Widget extends WP_Widget {
    
    public function __construct() {
        parent::__construct(
            'mytheme_quick_links',
            __('Footer Quick Links', 'mytheme'),
            array('description' => __('Quick links widget for footer', 'mytheme'))
        );
    }
    
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        $title = !empty($instance['title']) ? $instance['title'] : 'Quick Links';
        echo $args['before_title'] . esc_html($title) . $args['after_title'];
        
        echo '<ul class="footer-list">';
        
        // Get links from instance
        for ($i = 1; $i <= 6; $i++) {
            $link_text = !empty($instance["link_text_$i"]) ? $instance["link_text_$i"] : '';
            $link_url = !empty($instance["link_url_$i"]) ? $instance["link_url_$i"] : '';
            
            if ($link_text && $link_url) {
                echo '<li><a href="' . esc_url($link_url) . '"><i class="fas fa-caret-right"></i> ' . esc_html($link_text) . '</a></li>';
            }
        }
        
        echo '</ul>';
        echo $args['after_widget'];
    }
    
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'Quick Links';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>"><?php _e('Title:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
        
        for ($i = 1; $i <= 6; $i++) {
            $link_text = !empty($instance["link_text_$i"]) ? $instance["link_text_$i"] : '';
            $link_url = !empty($instance["link_url_$i"]) ? $instance["link_url_$i"] : '';
            ?>
            <p>
                <label for="<?php echo $this->get_field_id("link_text_$i"); ?>"><?php echo sprintf(__('Link %d Text:', 'mytheme'), $i); ?></label>
                <input class="widefat" id="<?php echo $this->get_field_id("link_text_$i"); ?>" name="<?php echo $this->get_field_name("link_text_$i"); ?>" type="text" value="<?php echo esc_attr($link_text); ?>">
            </p>
            <p>
                <label for="<?php echo $this->get_field_id("link_url_$i"); ?>"><?php echo sprintf(__('Link %d URL:', 'mytheme'), $i); ?></label>
                <input class="widefat" id="<?php echo $this->get_field_id("link_url_$i"); ?>" name="<?php echo $this->get_field_name("link_url_$i"); ?>" type="url" value="<?php echo esc_attr($link_url); ?>">
            </p>
            <?php
        }
    }
    
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        
        for ($i = 1; $i <= 6; $i++) {
            $instance["link_text_$i"] = (!empty($new_instance["link_text_$i"])) ? sanitize_text_field($new_instance["link_text_$i"]) : '';
            $instance["link_url_$i"] = (!empty($new_instance["link_url_$i"])) ? esc_url_raw($new_instance["link_url_$i"]) : '';
        }
        
        return $instance;
    }
}

// Social Media Widget
class Mytheme_Social_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'mytheme_social',
            __('Footer Social Media', 'mytheme'),
            array('description' => __('Social media links widget for footer', 'mytheme'))
        );
    }

    public function widget($args, $instance) {
        echo $args['before_widget'];

        $title = !empty($instance['title']) ? $instance['title'] : 'Our Social';
        echo $args['before_title'] . esc_html($title) . $args['after_title'];

        echo '<ul class="footer-list social">';

        $social_networks = array(
            'facebook' => array('icon' => 'fab fa-facebook', 'name' => 'Facebook'),
            'twitter' => array('icon' => 'fab fa-x-twitter', 'name' => 'Twitter'),
            'instagram' => array('icon' => 'fab fa-instagram', 'name' => 'Instagram'),
            'youtube' => array('icon' => 'fab fa-youtube', 'name' => 'Youtube'),
            'whatsapp' => array('icon' => 'fab fa-whatsapp', 'name' => 'Whatsapp'),
            'linkedin' => array('icon' => 'fab fa-linkedin-in', 'name' => 'Linkedin')
        );

        foreach ($social_networks as $network => $data) {
            $url = !empty($instance[$network]) ? $instance[$network] : '';
            if ($url) {
                echo '<li><a href="' . esc_url($url) . '" target="_blank"><i class="' . esc_attr($data['icon']) . '"></i> ' . esc_html($data['name']) . '</a></li>';
            }
        }

        echo '</ul>';
        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'Our Social';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>"><?php _e('Title:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <?php

        $social_networks = array(
            'facebook' => 'Facebook',
            'twitter' => 'Twitter',
            'instagram' => 'Instagram',
            'youtube' => 'Youtube',
            'whatsapp' => 'Whatsapp',
            'linkedin' => 'Linkedin'
        );

        foreach ($social_networks as $network => $name) {
            $url = !empty($instance[$network]) ? $instance[$network] : '';
            ?>
            <p>
                <label for="<?php echo $this->get_field_id($network); ?>"><?php echo sprintf(__('%s URL:', 'mytheme'), $name); ?></label>
                <input class="widefat" id="<?php echo $this->get_field_id($network); ?>" name="<?php echo $this->get_field_name($network); ?>" type="url" value="<?php echo esc_attr($url); ?>">
            </p>
            <?php
        }
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';

        $social_networks = array('facebook', 'twitter', 'instagram', 'youtube', 'whatsapp', 'linkedin');
        foreach ($social_networks as $network) {
            $instance[$network] = (!empty($new_instance[$network])) ? esc_url_raw($new_instance[$network]) : '';
        }

        return $instance;
    }
}

// Contact Widget
class Mytheme_Contact_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'mytheme_contact',
            __('Footer Contact', 'mytheme'),
            array('description' => __('Contact information widget for footer', 'mytheme'))
        );
    }

    public function widget($args, $instance) {
        echo $args['before_widget'];

        $title = !empty($instance['title']) ? $instance['title'] : 'Get In Touch';
        echo $args['before_title'] . esc_html($title) . $args['after_title'];

        echo '<ul class="footer-contact">';

        // Phone
        $phone = !empty($instance['phone']) ? $instance['phone'] : '';
        if ($phone) {
            echo '<li><a href="tel:' . esc_attr($phone) . '"><i class="far fa-phone"></i>' . esc_html($phone) . '</a></li>';
        }

        // Address
        $address = !empty($instance['address']) ? $instance['address'] : '';
        if ($address) {
            echo '<li><i class="far fa-map-marker-alt"></i>' . esc_html($address) . '</li>';
        }

        // Email
        $email = !empty($instance['email']) ? $instance['email'] : '';
        if ($email) {
            echo '<li><a href="mailto:' . esc_attr($email) . '"><i class="far fa-envelope"></i>' . esc_html($email) . '</a></li>';
        }

        echo '</ul>';

        // Ticket button
        $button_text = !empty($instance['button_text']) ? $instance['button_text'] : 'Buy Ticket';
        $button_url = !empty($instance['button_url']) ? $instance['button_url'] : '#';

        echo '<div class="footer-request">';
        echo '<p>Book Your Ticket</p>';
        echo '<a href="' . esc_url($button_url) . '" class="theme-btn">' . esc_html($button_text) . '<i class="fas fa-arrow-right"></i></a>';
        echo '</div>';

        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'Get In Touch';
        $phone = !empty($instance['phone']) ? $instance['phone'] : '';
        $address = !empty($instance['address']) ? $instance['address'] : '';
        $email = !empty($instance['email']) ? $instance['email'] : '';
        $button_text = !empty($instance['button_text']) ? $instance['button_text'] : 'Buy Ticket';
        $button_url = !empty($instance['button_url']) ? $instance['button_url'] : '#';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>"><?php _e('Title:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('phone'); ?>"><?php _e('Phone:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('phone'); ?>" name="<?php echo $this->get_field_name('phone'); ?>" type="text" value="<?php echo esc_attr($phone); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('address'); ?>"><?php _e('Address:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('address'); ?>" name="<?php echo $this->get_field_name('address'); ?>" type="text" value="<?php echo esc_attr($address); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('email'); ?>"><?php _e('Email:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('email'); ?>" name="<?php echo $this->get_field_name('email'); ?>" type="email" value="<?php echo esc_attr($email); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('button_text'); ?>"><?php _e('Button Text:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('button_text'); ?>" name="<?php echo $this->get_field_name('button_text'); ?>" type="text" value="<?php echo esc_attr($button_text); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('button_url'); ?>"><?php _e('Button URL:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('button_url'); ?>" name="<?php echo $this->get_field_name('button_url'); ?>" type="url" value="<?php echo esc_attr($button_url); ?>">
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['phone'] = (!empty($new_instance['phone'])) ? sanitize_text_field($new_instance['phone']) : '';
        $instance['address'] = (!empty($new_instance['address'])) ? sanitize_text_field($new_instance['address']) : '';
        $instance['email'] = (!empty($new_instance['email'])) ? sanitize_email($new_instance['email']) : '';
        $instance['button_text'] = (!empty($new_instance['button_text'])) ? sanitize_text_field($new_instance['button_text']) : '';
        $instance['button_url'] = (!empty($new_instance['button_url'])) ? esc_url_raw($new_instance['button_url']) : '';

        return $instance;
    }
}

// Register widgets
function mytheme_register_custom_widgets() {
    register_widget('Mytheme_Newsletter_Widget');
    register_widget('Mytheme_Quick_Links_Widget');
    register_widget('Mytheme_Social_Widget');
    register_widget('Mytheme_Contact_Widget');
}
add_action('widgets_init', 'mytheme_register_custom_widgets');
