<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<header class="header">
	<!-- Header Top -->
	<div class="header-top">
            <div class="container">
                <div class="header-top-wrap">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="header-top-left">
                                <div class="header-top-contact">
                                    <ul>
                                        <li><a href="#"><i class="far fa-location-dot"></i>
                                            25/B Milford Road, New York</a></li>
                                        <li><a href="mailto:<EMAIL>"><i class="far fa-envelopes"></i>
                                                <EMAIL></a></li>
                                        <li><a href="tel:+21236547898"><i class="far fa-phone-volume"></i> ****** 654 7898</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="header-top-right">
                                <div class="header-top-lang">
                                    <div class="dropdown">
                                        <a href="#" class="top-lang dropdown-toggle" data-bs-toggle="dropdown"><i
                                                class="fal fa-globe"></i> Language</a>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" href="#">English</a></li>
                                            <li><a class="dropdown-item" href="#">German</a></li>
                                            <li><a class="dropdown-item" href="#">Russian</a></li>
                                            <li><a class="dropdown-item" href="#">Spanish</a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="header-top-social">
                                    <span>Follow Us:</span>
                                    <a href="#"><i class="fab fa-facebook"></i></a>
                                    <a href="#"><i class="fab fa-x-twitter"></i></a>
                                    <a href="#"><i class="fab fa-instagram"></i></a>
                                    <a href="#"><i class="fab fa-linkedin"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
	
    <div class="main-navigation">
        <nav class="navbar navbar-expand-lg">
            <div class="container position-relative">
                    <?php if (has_custom_logo()) : ?>
						<div class="navbar-brand">
							<?php the_custom_logo(); ?>
						</div>
					<?php else : ?>
						<a href="<?php echo esc_url(home_url('/')); ?>" class="navbar-brand">
							<?php bloginfo('name'); ?>
						</a>
					<?php endif; ?>


                <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                        data-bs-target="#main_nav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-mobile-icon"><i class="far fa-bars"></i></span>
                </button>
                <div class="collapse navbar-collapse" id="main_nav">
                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'main-menu',
                        'container' => false,
                        'menu_class' => 'navbar-nav',
                        'walker' => new Bootstrap_NavWalker()
                    ));
                    ?>
                </div>
            </div>
        </nav>
    </div>
</header>
