<?php
// Theme setup
function mytheme_setup() {
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
	add_theme_support('custom-background', array(
		'default-color' => 'ffffff',
		'default-image' => '',
	));
    register_nav_menu('main-menu', __('Main Menu'));
}
add_action('after_setup_theme', 'mytheme_setup');

// Enqueue styles and scripts
function mytheme_enqueue_scripts() {
    // Styles
    wp_enqueue_style('bootstrap', get_template_directory_uri() . '/assets/css/bootstrap.min.css');
    wp_enqueue_style('fontawesome', get_template_directory_uri() . '/assets/css/all-fontawesome.min.css');
    wp_enqueue_style('animate', get_template_directory_uri() . '/assets/css/animate.min.css');
    wp_enqueue_style('magnific-popup', get_template_directory_uri() . '/assets/css/magnific-popup.min.css');
    wp_enqueue_style('owl-carousel', get_template_directory_uri() . '/assets/css/owl.carousel.min.css');
    wp_enqueue_style('template-style', get_template_directory_uri() . '/assets/css/style.css'); // Original template styles
    wp_enqueue_style('main-style', get_stylesheet_uri()); // WordPress theme overrides (load last)

    // Scripts
    wp_enqueue_script('jquery'); // WordPress's built-in jQuery
    wp_enqueue_script('modernizr', get_template_directory_uri() . '/assets/js/modernizr.min.js', [], null, true);
    wp_enqueue_script('bootstrap', get_template_directory_uri() . '/assets/js/bootstrap.bundle.min.js', ['jquery'], null, true);
    wp_enqueue_script('imagesloaded', get_template_directory_uri() . '/assets/js/imagesloaded.pkgd.min.js', ['jquery'], null, true);
    wp_enqueue_script('magnific-popup', get_template_directory_uri() . '/assets/js/jquery.magnific-popup.min.js', ['jquery'], null, true);
    wp_enqueue_script('isotope', get_template_directory_uri() . '/assets/js/isotope.pkgd.min.js', ['jquery'], null, true);
    wp_enqueue_script('appear', get_template_directory_uri() . '/assets/js/jquery.appear.min.js', ['jquery'], null, true);
    wp_enqueue_script('easing', get_template_directory_uri() . '/assets/js/jquery.easing.min.js', ['jquery'], null, true);
    wp_enqueue_script('owl-carousel', get_template_directory_uri() . '/assets/js/owl.carousel.min.js', ['jquery'], null, true);
    wp_enqueue_script('counter-up', get_template_directory_uri() . '/assets/js/counter-up.js', ['jquery'], null, true);
    wp_enqueue_script('wow', get_template_directory_uri() . '/assets/js/wow.min.js', ['jquery'], null, true);
    wp_enqueue_script('countdown', get_template_directory_uri() . '/assets/js/countdown.min.js', ['jquery'], null, true);
    wp_enqueue_script('main-js', get_template_directory_uri() . '/assets/js/main.js', ['jquery', 'owl-carousel'], null, true);
	
}
add_action('wp_enqueue_scripts', 'mytheme_enqueue_scripts');

// Register widget areas
function mytheme_widgets_init() {
    // Footer About Us Widget Area
    register_sidebar(array(
        'name'          => __('Footer About Us', 'mytheme'),
        'id'            => 'footer-about',
        'description'   => __('Add widgets for the footer about us section', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box about-us">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));

    // Footer Quick Links Widget Area
    register_sidebar(array(
        'name'          => __('Footer Quick Links', 'mytheme'),
        'id'            => 'footer-links',
        'description'   => __('Add widgets for footer quick links', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box list">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));

    // Footer Social Media Widget Area
    register_sidebar(array(
        'name'          => __('Footer Social Media', 'mytheme'),
        'id'            => 'footer-social',
        'description'   => __('Add widgets for footer social media links', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box list">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));

    // Footer Contact Widget Area
    register_sidebar(array(
        'name'          => __('Footer Contact', 'mytheme'),
        'id'            => 'footer-contact',
        'description'   => __('Add widgets for footer contact information', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box list">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'mytheme_widgets_init');

// Include nav walker
require_once get_template_directory() . '/class-bootstrap-navwalker.php';

// Include custom widgets
require_once get_template_directory() . '/inc/custom-widgets.php';

// Auto-setup footer widgets with default content (runs once)
function mytheme_setup_default_widgets() {
    if (get_option('mytheme_widgets_setup') !== 'done') {

        // Setup Newsletter Widget
        $newsletter_widget = array(
            'description' => 'We are many variations of passages available majority have suffered in some injected content of a page when looking at its layout humour words believable.'
        );

        // Setup Quick Links Widget
        $links_widget = array(
            'title' => 'Quick Links',
            'link_text_1' => 'About Us',
            'link_url_1' => '#',
            'link_text_2' => 'Update News',
            'link_url_2' => '#',
            'link_text_3' => 'Contact Us',
            'link_url_3' => '#',
            'link_text_4' => 'Testimonials',
            'link_url_4' => '#',
            'link_text_5' => 'Terms Of Service',
            'link_url_5' => '#',
            'link_text_6' => 'Privacy Policy',
            'link_url_6' => '#'
        );

        // Setup Social Widget
        $social_widget = array(
            'title' => 'Our Social',
            'facebook' => 'https://facebook.com',
            'twitter' => 'https://twitter.com',
            'instagram' => 'https://instagram.com',
            'youtube' => 'https://youtube.com',
            'whatsapp' => 'https://whatsapp.com',
            'linkedin' => 'https://linkedin.com'
        );

        // Setup Contact Widget
        $contact_widget = array(
            'title' => 'Get In Touch',
            'phone' => '****** 654 7898',
            'address' => '25/B Milford Road, New York',
            'email' => '<EMAIL>',
            'button_text' => 'Buy Ticket',
            'button_url' => '#'
        );

        // Mark as setup complete
        update_option('mytheme_widgets_setup', 'done');
    }
}
add_action('after_switch_theme', 'mytheme_setup_default_widgets');
?>
