<?php
// Theme setup
function mytheme_setup() {
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
	add_theme_support('custom-background', array(
		'default-color' => 'ffffff',
		'default-image' => '',
	));
    register_nav_menu('main-menu', __('Main Menu'));
}
add_action('after_setup_theme', 'mytheme_setup');

// Enqueue styles and scripts
function mytheme_enqueue_scripts() {
    // Styles
    wp_enqueue_style('bootstrap', get_template_directory_uri() . '/assets/css/bootstrap.min.css');
    wp_enqueue_style('fontawesome', get_template_directory_uri() . '/assets/css/all-fontawesome.min.css');
    wp_enqueue_style('animate', get_template_directory_uri() . '/assets/css/animate.min.css');
    wp_enqueue_style('magnific-popup', get_template_directory_uri() . '/assets/css/magnific-popup.min.css');
    wp_enqueue_style('owl-carousel', get_template_directory_uri() . '/assets/css/owl.carousel.min.css');
    wp_enqueue_style('template-style', get_template_directory_uri() . '/assets/css/style.css'); // Original template styles
    wp_enqueue_style('main-style', get_stylesheet_uri()); // WordPress theme overrides (load last)

    // Scripts
    wp_enqueue_script('jquery'); // WordPress's built-in jQuery
    wp_enqueue_script('modernizr', get_template_directory_uri() . '/assets/js/modernizr.min.js', [], null, true);
    wp_enqueue_script('bootstrap', get_template_directory_uri() . '/assets/js/bootstrap.bundle.min.js', ['jquery'], null, true);
    wp_enqueue_script('imagesloaded', get_template_directory_uri() . '/assets/js/imagesloaded.pkgd.min.js', ['jquery'], null, true);
    wp_enqueue_script('magnific-popup', get_template_directory_uri() . '/assets/js/jquery.magnific-popup.min.js', ['jquery'], null, true);
    wp_enqueue_script('isotope', get_template_directory_uri() . '/assets/js/isotope.pkgd.min.js', ['jquery'], null, true);
    wp_enqueue_script('appear', get_template_directory_uri() . '/assets/js/jquery.appear.min.js', ['jquery'], null, true);
    wp_enqueue_script('easing', get_template_directory_uri() . '/assets/js/jquery.easing.min.js', ['jquery'], null, true);
    wp_enqueue_script('owl-carousel', get_template_directory_uri() . '/assets/js/owl.carousel.min.js', ['jquery'], null, true);
    wp_enqueue_script('counter-up', get_template_directory_uri() . '/assets/js/counter-up.js', ['jquery'], null, true);
    wp_enqueue_script('wow', get_template_directory_uri() . '/assets/js/wow.min.js', ['jquery'], null, true);
    wp_enqueue_script('countdown', get_template_directory_uri() . '/assets/js/countdown.min.js', ['jquery'], null, true);
    wp_enqueue_script('main-js', get_template_directory_uri() . '/assets/js/main.js', ['jquery', 'owl-carousel'], null, true);
	
}
add_action('wp_enqueue_scripts', 'mytheme_enqueue_scripts');

// Include nav walker
require_once get_template_directory() . '/class-bootstrap-navwalker.php';
?>
